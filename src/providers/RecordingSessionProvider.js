/**
 * RecordingSessionProvider - Isolated Auto-Advance Logic
 * Handles recording session state, phrase progression, and auto-advance functionality
 * This provider isolates the complex auto-advance logic to prevent race conditions
 */

import React, { createContext, useContext, useReducer, useEffect, useCallback } from 'react';
import { phraseCollectionConfig } from '../phrases';
import {
  getSelectedPhrases,
  saveSelectedPhrases
} from '../services/phraseRotationService';

// Action types
const RECORDING_ACTIONS = {
  SET_SELECTED_PHRASES: 'SET_SELECTED_PHRASES',
  SET_CURRENT_PHRASE_INDEX: 'SET_CURRENT_PHRASE_INDEX',
  SET_CURRENT_RECORDING_NUMBER: 'SET_CURRENT_RECORDING_NUMBER',
  SET_SELECTED_CATEGORY: 'SET_SELECTED_CATEGORY',
  UPDATE_RECORDINGS_COUNT: 'UPDATE_RECORDINGS_COUNT',
  SET_COMPLETION_PROMPT: 'SET_COMPLETION_PROMPT',
  RESET_RECORDING_SESSION: 'RESET_RECORDING_SESSION',
  SET_PHRASES_SELECTED: 'SET_PHRASES_SELECTED'
};

// Initial state
const initialState = {
  selectedPhrases: null,
  currentPhraseIndex: 0,
  currentRecordingNumber: 1,
  selectedCategory: '',
  recordingsCount: {},
  showCompletionPrompt: false,
  phrasesSelected: false,
  
  // Configuration
  RECORDINGS_PER_PHRASE: phraseCollectionConfig.recordingsPerPhrase,
  COLLECTION_GOAL: phraseCollectionConfig.collectionGoal
};

// Reducer with focused state management
const recordingSessionReducer = (state, action) => {
  switch (action.type) {
    case RECORDING_ACTIONS.SET_SELECTED_PHRASES:
      return {
        ...state,
        selectedPhrases: action.payload,
        currentPhraseIndex: 0,
        selectedCategory: action.payload?.[0]?.category || ''
      };
      
    case RECORDING_ACTIONS.SET_CURRENT_PHRASE_INDEX:
      return {
        ...state,
        currentPhraseIndex: action.payload
      };
      
    case RECORDING_ACTIONS.SET_CURRENT_RECORDING_NUMBER:
      return {
        ...state,
        currentRecordingNumber: action.payload
      };
      
    case RECORDING_ACTIONS.SET_SELECTED_CATEGORY:
      return {
        ...state,
        selectedCategory: action.payload
      };
      
    case RECORDING_ACTIONS.UPDATE_RECORDINGS_COUNT:
      const newRecordingsCount = {
        ...state.recordingsCount,
        [action.payload.phraseKey]: action.payload.count
      };
      
      // Persist to localStorage
      try {
        localStorage.setItem('icuAppRecordingsCount', JSON.stringify(newRecordingsCount));
      } catch (error) {
        console.warn('⚠️ Failed to save recording counts to localStorage:', error);
      }
      
      return {
        ...state,
        recordingsCount: newRecordingsCount
      };
      
    case RECORDING_ACTIONS.SET_COMPLETION_PROMPT:
      return {
        ...state,
        showCompletionPrompt: action.payload
      };
      
    case RECORDING_ACTIONS.SET_PHRASES_SELECTED:
      return {
        ...state,
        phrasesSelected: action.payload
      };
      
    case RECORDING_ACTIONS.RESET_RECORDING_SESSION:
      return {
        ...initialState,
        recordingsCount: state.recordingsCount // Preserve recording counts
      };
      
    default:
      return state;
  }
};

// Context
const RecordingSessionContext = createContext();

// Provider component
export const RecordingSessionProvider = ({ children }) => {
  const [state, dispatch] = useReducer(recordingSessionReducer, initialState);

  // Load recording counts from localStorage on mount
  useEffect(() => {
    try {
      const savedRecordingsCount = localStorage.getItem('icuAppRecordingsCount');
      if (savedRecordingsCount) {
        const parsedCounts = JSON.parse(savedRecordingsCount);
        
        // Update state with loaded counts
        Object.entries(parsedCounts).forEach(([phraseKey, count]) => {
          dispatch({
            type: RECORDING_ACTIONS.UPDATE_RECORDINGS_COUNT,
            payload: { phraseKey, count }
          });
        });
        
        console.log('📱 Recording counts loaded from localStorage:', parsedCounts);
      }
    } catch (error) {
      console.warn('⚠️ Failed to load recording counts from localStorage:', error);
    }

    // Clear selected phrases on page refresh to ensure fresh sessions
    const isPageRefresh = !sessionStorage.getItem('icuAppSessionActive');

    if (isPageRefresh) {
      // Clear selected phrases for fresh session - use correct localStorage keys
      try {
        localStorage.removeItem('icu_selected_phrases'); // Correct key used by phraseRotationService
        localStorage.removeItem('icuAppSelectedPhrases'); // Legacy key cleanup
        console.log('🔄 Fresh session: Selected phrases cleared for new session');
      } catch (error) {
        console.warn('⚠️ Failed to clear selected phrases:', error);
      }
    } else {
      // Load selected phrases if session is already active (within-session navigation)
      const loadedPhrases = getSelectedPhrases();
      if (loadedPhrases && loadedPhrases.length > 0) {
        dispatch({
          type: RECORDING_ACTIONS.SET_SELECTED_PHRASES,
          payload: loadedPhrases
        });
        dispatch({
          type: RECORDING_ACTIONS.SET_PHRASES_SELECTED,
          payload: true
        });
        console.log('📱 Selected phrases loaded from localStorage for session persistence');
      }
    }
  }, []);

  // AUTO-ADVANCE EFFECT - watches for recording count changes and triggers advancement
  useEffect(() => {
    console.log('🔄 AUTO-ADVANCE EFFECT TRIGGERED:', {
      hasSelectedPhrases: !!state.selectedPhrases,
      phrasesLength: state.selectedPhrases?.length || 0,
      currentPhraseIndex: state.currentPhraseIndex,
      recordingsCount: state.recordingsCount,
      timestamp: new Date().toISOString()
    });

    if (!state.selectedPhrases || state.selectedPhrases.length === 0 || state.currentPhraseIndex < 0) {
      console.log('🔄 AUTO-ADVANCE: Early return - no phrases or invalid index');
      return;
    }

    const currentPhraseObj = state.selectedPhrases[state.currentPhraseIndex];
    if (!currentPhraseObj) {
      console.log('🔄 AUTO-ADVANCE: Early return - no current phrase object');
      return;
    }

    const phraseKey = `${currentPhraseObj.category}:${currentPhraseObj.phrase}`;
    const currentCount = state.recordingsCount[phraseKey] || 0;

    console.log('🔄 AUTO-ADVANCE CHECK:', {
      phrase: currentPhraseObj.phrase,
      phraseKey,
      currentCount,
      required: state.RECORDINGS_PER_PHRASE,
      shouldAdvance: currentCount >= state.RECORDINGS_PER_PHRASE
    });

    if (currentCount >= state.RECORDINGS_PER_PHRASE) {
      console.log('🎯 AUTO-ADVANCE: Phrase completion detected, calling handleNextPhrase');

      // Use a small delay to ensure state is fully updated (like the working implementation)
      setTimeout(() => {
        console.log('🚀 AUTO-ADVANCE: Executing handleNextPhrase');
        handleNextPhrase();
      }, 50);
    }
  }, [state.recordingsCount, state.currentPhraseIndex, state.selectedPhrases, state.RECORDINGS_PER_PHRASE, handleNextPhrase]);

  // Handle next phrase progression - this is the key function for auto-advance
  const handleNextPhrase = useCallback(() => {
    console.log('🚀 === HANDLE NEXT PHRASE CALLED ===');
    console.log('🔍 IMMEDIATE STATE CHECK:');
    console.log('  selectedPhrases:', state.selectedPhrases);
    console.log('  currentPhraseIndex:', state.currentPhraseIndex);
    console.log('  recordingsCount:', state.recordingsCount);

    if (!state.selectedPhrases || state.selectedPhrases.length === 0) {
      console.log('❌ No phrases selected');
      return;
    }

    // Check if this is the last phrase
    if (state.currentPhraseIndex >= state.selectedPhrases.length - 1) {
      console.log('🏁 LAST PHRASE: Checking if all phrases are completed...');

      // Verify all phrases are completed before showing completion prompt
      const incompletePhrase = state.selectedPhrases.find(phrase => {
        const phraseKey = `${phrase.category}:${phrase.phrase}`;
        const count = state.recordingsCount[phraseKey] || 0;
        return count < state.RECORDINGS_PER_PHRASE;
      });

      if (!incompletePhrase) {
        console.log('🏁 ALL PHRASES COMPLETED: Showing completion prompt');
        dispatch({
          type: RECORDING_ACTIONS.SET_COMPLETION_PROMPT,
          payload: true
        });
      } else {
        console.log('⚠️ NOT ALL PHRASES COMPLETED YET:', incompletePhrase);
      }
    } else {
      console.log('📝 ADVANCING TO NEXT PHRASE');
      const nextPhraseIndex = state.currentPhraseIndex + 1;
      const nextPhrase = state.selectedPhrases[nextPhraseIndex];

      console.log('📝 Next phrase details:', {
        nextPhraseIndex,
        nextPhrase: nextPhrase?.phrase,
        nextCategory: nextPhrase?.category
      });

      // Update phrase index
      dispatch({
        type: RECORDING_ACTIONS.SET_CURRENT_PHRASE_INDEX,
        payload: nextPhraseIndex
      });

      // Update category if needed
      if (nextPhrase.category !== state.selectedCategory) {
        console.log('📝 Updating category from', state.selectedCategory, 'to', nextPhrase.category);
        dispatch({
          type: RECORDING_ACTIONS.SET_SELECTED_CATEGORY,
          payload: nextPhrase.category
        });
      }

      // Set recording number for next phrase
      const nextPhraseKey = `${nextPhrase.category}:${nextPhrase.phrase}`;
      const existingRecordings = state.recordingsCount[nextPhraseKey] || 0;
      console.log('📝 Setting recording number to', existingRecordings + 1);
      dispatch({
        type: RECORDING_ACTIONS.SET_CURRENT_RECORDING_NUMBER,
        payload: existingRecordings + 1
      });
    }
  }, [state.selectedPhrases, state.currentPhraseIndex, state.recordingsCount, state.RECORDINGS_PER_PHRASE, state.selectedCategory]);

  // Action creators
  const actions = {
    setSelectedPhrases: useCallback((phrases) => {
      dispatch({
        type: RECORDING_ACTIONS.SET_SELECTED_PHRASES,
        payload: phrases
      });
      dispatch({
        type: RECORDING_ACTIONS.SET_PHRASES_SELECTED,
        payload: true
      });
      saveSelectedPhrases(phrases);
    }, []),
    
    recordingCompleted: useCallback((metadata) => {
      console.log('📹 RECORDING COMPLETED FUNCTION CALLED:', {
        metadata,
        currentState: {
          currentPhraseIndex: state.currentPhraseIndex,
          selectedPhrases: state.selectedPhrases?.map(p => p.phrase),
          recordingsCount: state.recordingsCount
        }
      });

      const phraseKey = `${metadata.category}:${metadata.phrase}`;
      const currentCount = state.recordingsCount[phraseKey] || 0;
      const newCount = currentCount + 1;

      console.log('📹 RECORDING COMPLETED - COUNT UPDATE:', {
        phrase: metadata.phrase,
        category: metadata.category,
        phraseKey,
        previousCount: currentCount,
        newCount,
        willTriggerAutoAdvance: newCount >= state.RECORDINGS_PER_PHRASE
      });

      // Update recording count - this will trigger auto-advance useEffect
      dispatch({
        type: RECORDING_ACTIONS.UPDATE_RECORDINGS_COUNT,
        payload: { phraseKey, count: newCount }
      });

      // Update current recording number
      dispatch({
        type: RECORDING_ACTIONS.SET_CURRENT_RECORDING_NUMBER,
        payload: newCount + 1
      });

      console.log('📹 RECORDING COMPLETED - DISPATCHES SENT, useEffect should trigger now');
    }, [state.recordingsCount, state.currentPhraseIndex, state.selectedPhrases, state.RECORDINGS_PER_PHRASE]),
    
    setCurrentPhraseIndex: useCallback((index) => {
      dispatch({
        type: RECORDING_ACTIONS.SET_CURRENT_PHRASE_INDEX,
        payload: index
      });
    }, []),
    
    setCompletionPrompt: useCallback((show) => {
      dispatch({
        type: RECORDING_ACTIONS.SET_COMPLETION_PROMPT,
        payload: show
      });
    }, []),
    
    resetSession: useCallback(() => {
      dispatch({ type: RECORDING_ACTIONS.RESET_RECORDING_SESSION });
    }, [])
  };

  // Computed values
  const currentPhrase = state.selectedPhrases && state.selectedPhrases[state.currentPhraseIndex]
    ? state.selectedPhrases[state.currentPhraseIndex].phrase
    : '';

  const value = {
    ...state,
    ...actions,
    currentPhrase,
    handleNextPhrase
  };

  return (
    <RecordingSessionContext.Provider value={value}>
      {children}
    </RecordingSessionContext.Provider>
  );
};

// Hook to use recording session
export const useRecordingSession = () => {
  const context = useContext(RecordingSessionContext);
  if (!context) {
    throw new Error('useRecordingSession must be used within a RecordingSessionProvider');
  }
  return context;
};

export default RecordingSessionProvider;
